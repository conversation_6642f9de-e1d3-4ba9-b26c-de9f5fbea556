import React, { useState } from 'react';
import {
  MapPin,
  Clock,
  Briefcase,
  Star,
  Heart,
  CheckCircle,
  Mail,
  Zap,
  Shield,
  Target,
  Globe,
  TrendingUp,
  Award,
  Users
} from 'lucide-react';
import PageLayout from '../../components/layout/PageLayout';

// Enhanced job listings with more details
const jobListings = [
  {
    id: 1,
    title: 'Senior Software Engineer',
    location: 'Chennai, India',
    type: 'Full-time',
    experience: '3-5 years',
    department: 'Engineering',
    description: 'Develop and maintain cutting-edge web applications using modern technologies. Work with React, Node.js, and cloud platforms.',
    requirements: ['Bachelor\'s degree in Computer Science', '3+ years React experience', 'Strong problem-solving skills', 'Experience with cloud platforms'],
    benefits: ['Competitive salary', 'Health insurance', 'Flexible working hours', 'Professional development']
  },
  {
    id: 2,
    title: 'Sales Manager',
    location: 'Mumbai, India',
    type: 'Full-time',
    experience: '5-8 years',
    department: 'Sales',
    description: 'Lead our dynamic sales team and drive business growth. Develop strategic partnerships and expand market presence.',
    requirements: ['MBA or equivalent', '5+ years sales experience', 'Leadership skills', 'Industry knowledge'],
    benefits: ['Performance bonuses', 'Travel allowances', 'Team leadership opportunities', 'Career advancement']
  },
  {
    id: 3,
    title: 'Product Designer',
    location: 'Bangalore, India',
    type: 'Full-time',
    experience: '2-4 years',
    department: 'Design',
    description: 'Create intuitive and beautiful user experiences for our energy solutions platform. Work closely with engineering teams.',
    requirements: ['Design degree or portfolio', 'UI/UX experience', 'Figma/Sketch proficiency', 'User research skills'],
    benefits: ['Creative environment', 'Latest design tools', 'Conference attendance', 'Design mentorship']
  },
  {
    id: 4,
    title: 'DevOps Engineer',
    location: 'Hyderabad, India',
    type: 'Full-time',
    experience: '3-6 years',
    department: 'Engineering',
    description: 'Build and maintain robust infrastructure for our energy management systems. Ensure scalability and reliability.',
    requirements: ['DevOps experience', 'AWS/Azure knowledge', 'Docker/Kubernetes', 'CI/CD pipelines'],
    benefits: ['Remote work options', 'Certification support', 'Infrastructure budget', 'On-call compensation']
  }
];

// Company values and culture
const companyValues = [
  {
    icon: <Zap className="w-8 h-8" />,
    title: 'Innovation',
    description: 'We push boundaries in energy technology and create solutions that matter.'
  },
  {
    icon: <Shield className="w-8 h-8" />,
    title: 'Reliability',
    description: 'Our commitment to quality and dependability drives everything we do.'
  },
  {
    icon: <Target className="w-8 h-8" />,
    title: 'Excellence',
    description: 'We strive for perfection in every project and continuously improve.'
  },
  {
    icon: <Globe className="w-8 h-8" />,
    title: 'Global Impact',
    description: 'Our solutions make a difference in energy efficiency worldwide.'
  }
];

const Careers: React.FC = () => {
  const [form, setForm] = useState({
    name: '',
    email: '',
    phone: '',
    position: '',
    experience: '',
    coverLetter: '',
    resume: null
  });
  const [submitted, setSubmitted] = useState(false);
  const [selectedJob, setSelectedJob] = useState<number | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, files } = e.target as any;
    setForm((prev) => ({
      ...prev,
      [name]: files ? files[0] : value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitted(true);
    // Here you would handle sending the application data to your backend
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <PageLayout hideHero hideBreadcrumbs>
      <div className="font-['Open_Sans']">
        {/* Apply Open Sans font family consistently */}
        <style>{`
          nav.mb-10 { display: none !important; }
          .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }

          /* Apply Open Sans font family consistently */
          * {
            font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
          }

          /* Ensure proper font weights and sizes */
          h1, h2, h3, h4, h5, h6 {
            font-family: 'Open Sans', sans-serif !important;
            font-weight: 700 !important;
          }

          p, span, div {
            font-family: 'Open Sans', sans-serif !important;
          }

          button {
            font-family: 'Open Sans', sans-serif !important;
            font-weight: 600 !important;
          }
        `}</style>

        {/* Simple Title Section */}
        <section className="py-16 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Careers at Atandra Energy
            </h1>
            <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Join our team and help shape the future of energy technology. We're looking for talented individuals who share our passion for innovation and excellence.
            </p>
          </div>
        </section>

        {/* Company Culture Section - Simplified */}
        <section id="culture" className="py-16 bg-gray-50">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                Our Values
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                We believe in creating an environment where innovation thrives and people grow.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {companyValues.map((value, index) => (
                <div
                  key={index}
                  className="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100"
                >
                  <div className="bg-blue-100 rounded-lg p-3 w-fit mb-4">
                    <div className="text-blue-600">{value.icon}</div>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">{value.title}</h3>
                  <p className="text-gray-600 text-sm leading-relaxed">{value.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Job Listings Section - Simplified */}
        <section id="jobs" className="py-16 bg-white">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                Current Openings
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Explore exciting opportunities to grow your career with us.
              </p>
            </div>

            <div className="space-y-6">
              {jobListings.map((job, index) => (
                <div
                  key={job.id}
                  className="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-200"
                >
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {job.title}
                      </h3>
                      <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                        <span className="flex items-center">
                          <MapPin className="w-4 h-4 mr-1" />
                          {job.location}
                        </span>
                        <span className="flex items-center">
                          <Clock className="w-4 h-4 mr-1" />
                          {job.type}
                        </span>
                        <span className="flex items-center">
                          <Briefcase className="w-4 h-4 mr-1" />
                          {job.experience}
                        </span>
                      </div>
                    </div>
                    <div className="mt-4 md:mt-0 flex items-center gap-3">
                      <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                        {job.department}
                      </span>
                      <button
                        onClick={() => {
                          setForm(prev => ({ ...prev, position: job.title }));
                          scrollToSection('application');
                        }}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-300"
                      >
                        Apply Now
                      </button>
                    </div>
                  </div>

                  <p className="text-gray-700 mb-4">{job.description}</p>

                  <div className="flex justify-between items-center">
                    <button
                      onClick={() => setSelectedJob(selectedJob === job.id ? null : job.id)}
                      className="text-blue-600 hover:text-blue-700 font-medium transition-colors duration-300"
                    >
                      {selectedJob === job.id ? 'Show Less' : 'View Details'}
                    </button>
                  </div>

                  {/* Expanded Details */}
                  {selectedJob === job.id && (
                    <div className="mt-6 pt-6 border-t border-gray-200">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="font-medium text-gray-900 mb-3">Requirements:</h4>
                          <ul className="space-y-2">
                            {job.requirements.map((req, reqIndex) => (
                              <li key={reqIndex} className="flex items-start space-x-2">
                                <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0 mt-0.5" />
                                <span className="text-gray-600 text-sm">{req}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900 mb-3">Benefits:</h4>
                          <ul className="space-y-2">
                            {job.benefits.map((benefit, benefitIndex) => (
                              <li key={benefitIndex} className="flex items-start space-x-2">
                                <Star className="w-4 h-4 text-yellow-500 flex-shrink-0 mt-0.5" />
                                <span className="text-gray-600 text-sm">{benefit}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Application Form Section - Simplified */}
        <section id="application" className="py-16 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                Apply Now
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Ready to join our team? Submit your application and we'll get back to you soon.
              </p>
            </div>

            {submitted ? (
              <div className="bg-green-50 border border-green-200 rounded-lg p-8 text-center">
                <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-green-800 mb-2">Application Submitted!</h3>
                <p className="text-green-700 mb-6">
                  Thank you for your interest. We'll review your application and get back to you within 5-7 business days.
                </p>
                <button
                  onClick={() => {
                    setSubmitted(false);
                    setForm({ name: '', email: '', phone: '', position: '', experience: '', coverLetter: '', resume: null });
                  }}
                  className="bg-green-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors duration-300"
                >
                  Submit Another Application
                </button>
              </div>
            ) : (
              <div className="bg-gray-50 rounded-lg p-8">
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Full Name *
                      </label>
                      <input
                        name="name"
                        type="text"
                        placeholder="Enter your full name"
                        value={form.name}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-300"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Email Address *
                      </label>
                      <input
                        name="email"
                        type="email"
                        placeholder="Enter your email"
                        value={form.email}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-300"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number
                      </label>
                      <input
                        name="phone"
                        type="tel"
                        placeholder="Enter your phone number"
                        value={form.phone}
                        onChange={handleChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-300"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Position *
                      </label>
                      <select
                        name="position"
                        value={form.position}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-300"
                      >
                        <option value="">Select Position</option>
                        {jobListings.map((job) => (
                          <option key={job.id} value={job.title}>{job.title}</option>
                        ))}
                        <option value="General Application">General Application</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Years of Experience
                    </label>
                    <select
                      name="experience"
                      value={form.experience}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-300"
                    >
                      <option value="">Select experience level</option>
                      <option value="0-1">0-1 years (Entry Level)</option>
                      <option value="2-3">2-3 years</option>
                      <option value="4-6">4-6 years</option>
                      <option value="7-10">7-10 years</option>
                      <option value="10+">10+ years (Senior Level)</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Cover Letter
                    </label>
                    <textarea
                      name="coverLetter"
                      placeholder="Tell us why you're interested in this position..."
                      value={form.coverLetter}
                      onChange={handleChange}
                      rows={4}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-300 resize-none"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Resume/CV *
                    </label>
                    <input
                      name="resume"
                      type="file"
                      accept=".pdf,.doc,.docx"
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-300"
                    />
                    <p className="text-sm text-gray-500 mt-2">
                      Accepted formats: PDF, DOC, DOCX (Max size: 5MB)
                    </p>
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-300"
                  >
                    Submit Application
                  </button>
                </form>
              </div>
            )}
          </div>
        </section>

        {/* Contact Section */}
        <section className="py-12 bg-blue-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Questions About Working With Us?
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              Our HR team is here to help you understand more about opportunities at Atandra Energy.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="mailto:<EMAIL>"
                className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-300"
              >
                <Mail className="w-4 h-4 inline mr-2" />
                <EMAIL>
              </a>
              <a
                href="tel:+911234567890"
                className="border border-blue-600 text-blue-600 px-6 py-3 rounded-lg font-medium hover:bg-blue-600 hover:text-white transition-colors duration-300"
              >
                +91 12345 67890
              </a>
            </div>
          </div>
        </section>
      </div>
    </PageLayout>
  );
};

export default Careers;